from core.auth import AuthMiddleware, AuthenticationError
from config.logger import setup_logging
from config.config_loader import get_config_from_api
import aiohttp
import asyncio
import re
import time
import json
import os
import pickle
from typing import Dict, Set, List, Optional, Union
from datetime import datetime, timedelta

TAG = __name__
logger = setup_logging()


class UnregisteredMacError(AuthenticationError):
    """未注册的MAC地址异常"""

    def __init__(self, mac_address):
        self.mac_address = mac_address
        super().__init__(f"未注册的MAC地址: {mac_address}，请前往官网注册您的设备")


# 缓存文件路径
CACHE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "cache")
MAC_CACHE_FILE = os.path.join(CACHE_DIR, "mac_cache.pkl")
MAC_BLACKLIST_CACHE_FILE = os.path.join(CACHE_DIR, "mac_blacklist_cache.pkl")


def _is_valid_mac_format(mac_address: str) -> bool:
    """检查MAC地址格式是否有效"""
    if not mac_address:
        return False

    # MAC地址格式检查（支持常见的MAC地址格式）
    mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    return bool(re.match(mac_pattern, mac_address))


class MacDBAuthMiddleware(AuthMiddleware):
    """基于数据库的MAC地址认证中间件"""

    def __init__(self, config):
        # 调用父类初始化，保留原有的token认证逻辑
        super().__init__(config)

        # 保存配置
        self.headers = None
        self.config = config

        # 获取数据库API配置，复用与auth.py相同的配置获取方式
        self.manager_api_config = config.get("manager-api", {})
        self.api_base_url = self.manager_api_config.get("url")
        self.api_key = self.manager_api_config.get("secret")

        # MAC地址缓存
        self.mac_addresses_cache: Set[str] = set()
        self.device_agent_mapping: Dict[str, str] = {}  # MAC地址 -> 智能体ID映射
        self.mac_blacklist: Set[str] = set()  # MAC地址黑名单
        self.last_refresh_time = 0
        self.cache_invalidated: Set[str] = set()  # 需要失效的MAC地址

        # 获取MAC认证配置
        self.mac_auth_config = config.get("mac_auth", {})

        # 缓存配置
        self.cache_ttl = self.mac_auth_config.get("cache_ttl", 86400)  # 缓存有效期，默认1天
        self.cache_retry_interval = self.mac_auth_config.get("cache_retry_interval", 300)  # 缓存重试间隔，默认5分钟
        self.max_retry_attempts = self.mac_auth_config.get("max_retry_attempts", 3)  # 最大重试次数
        self.cache_update_strategy = self.mac_auth_config.get("cache_update_strategy", "time_based")  # 缓存更新策略

        # 确保缓存目录存在
        os.makedirs(CACHE_DIR, exist_ok=True)

        # MAC地址访问频率限制
        self.mac_access_rate: Dict[str, List[float]] = {}  # MAC地址 -> 访问时间列表
        self.mac_access_limit = self.mac_auth_config.get("access_limit", 10)  # 每分钟最大访问次数
        self.mac_access_window = 60  # 访问频率窗口（秒）

        # 是否允许自动注册MAC地址
        self.auto_register = self.mac_auth_config.get("auto_register", False)

        # 是否启用设备指纹验证（防止MAC地址伪造）
        self.enable_device_fingerprint = self.mac_auth_config.get("enable_device_fingerprint", False)

        # 初始化允许设备白名单
        self.allowed_devices: Set[str] = set(self.mac_auth_config.get("allowed_devices", []))

        # 打印配置信息用于调试
        logger.bind(tag=TAG).info(f"MAC认证配置加载完成: enabled={self.mac_auth_config.get('enabled', False)}, auto_register={self.auto_register}, access_limit={self.mac_access_limit}")
        logger.bind(tag=TAG).debug(f"完整MAC认证配置: {self.mac_auth_config}")

        # 初始加载MAC地址缓存
        # 先尝试从本地缓存文件加载
        self._load_cache_from_file()
        # 然后启动异步任务初始化/更新缓存
        self.refresh_task = asyncio.create_task(self._init_mac_addresses())

        # 移除LRU缓存装饰器，因为异步函数无法直接使用functools.lru_cache
        # 改用简单的字典缓存
        self._device_agent_cache: Dict[str, str] = {}
        self._device_agent_cache_time: Dict[str, float] = {}

    async def _init_mac_addresses(self):
        """初始化MAC地址缓存"""
        try:
            # 如果本地缓存为空，尝试初始化
            if not self.mac_addresses_cache and not self.mac_blacklist:
                # 如果配置中包含MAC地址列表，直接使用
                if "mac_addresses" in self.config:
                    self.mac_addresses_cache = set(self.config["mac_addresses"])
                    logger.bind(tag=TAG).info(f"使用配置中的MAC地址列表，共 {len(self.mac_addresses_cache)} 个设备")
                else:
                    # 否则从API刷新MAC地址列表
                    await self._refresh_mac_addresses()

                # 如果配置中包含MAC地址黑名单，直接使用
                if "mac_blacklist" in self.config:
                    self.mac_blacklist = set(self.config["mac_blacklist"])
                    logger.bind(tag=TAG).info(f"使用配置中的MAC地址黑名单，共 {len(self.mac_blacklist)} 个设备")
                else:
                    # 否则从API刷新MAC地址黑名单
                    await self._refresh_mac_blacklist()

                # 如果配置中包含设备-智能体映射，直接使用
                if "device_agent_mapping" in self.config:
                    self.device_agent_mapping = self.config["device_agent_mapping"]
                    logger.bind(tag=TAG).info(f"使用配置中的设备-智能体映射，共 {len(self.device_agent_mapping)} 个映射")
                else:
                    # 否则从API刷新设备-智能体映射
                    await self._refresh_device_agent_mapping()

                # 保存缓存到文件
                self._save_cache_to_file()

            # 设置定期刷新任务
            asyncio.create_task(self._periodic_refresh())
        except Exception as e:
            logger.bind(tag=TAG).error(f"初始化MAC地址缓存失败: {str(e)}")

    async def _periodic_refresh(self):
        """定期刷新MAC地址缓存"""
        while True:
            # 使用较短的间隔检查缓存是否需要刷新
            await asyncio.sleep(self.cache_retry_interval)

            # 检查缓存是否过期
            current_time = time.time()
            if current_time - self.last_refresh_time >= self.cache_ttl:
                try:
                    logger.bind(tag=TAG).info("缓存已过期，开始刷新")
                    await self._refresh_mac_addresses()
                    await self._refresh_mac_blacklist()
                    await self._refresh_device_agent_mapping()

                    # 更新刷新时间
                    self.last_refresh_time = current_time

                    # 保存缓存到文件
                    self._save_cache_to_file()
                except Exception as e:
                    logger.bind(tag=TAG).error(f"刷新MAC地址缓存失败: {str(e)}")

    async def _refresh_mac_addresses(self):
        """从API刷新MAC地址列表"""
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            try:
                # 获取所有设备的MAC地址列表
                async with session.get(f"{self.api_base_url}/device/mac/maclist", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "data" in data and isinstance(data["data"], list):
                            self.mac_addresses_cache = set(data["data"])
                            self.last_refresh_time = time.time()
                            logger.bind(tag=TAG).info(f"已刷新MAC地址缓存，共 {len(self.mac_addresses_cache)} 个设备")
                        else:
                            logger.bind(tag=TAG).error(f"MAC地址数据格式错误: {data}")
                    else:
                        logger.bind(tag=TAG).error(f"获取MAC地址列表失败: {response.status}")
            except Exception as e:
                logger.bind(tag=TAG).error(f"请求MAC地址列表异常: {str(e)}")

    async def _refresh_mac_blacklist(self):
        """从API刷新MAC地址黑名单"""
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            try:
                async with session.get(f"{self.api_base_url}/device/mac/blacklist", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "data" in data and isinstance(data["data"], list):
                            self.mac_blacklist = set(data["data"])
                            logger.bind(tag=TAG).info(f"已刷新MAC地址黑名单，共 {len(self.mac_blacklist)} 个设备")
                        else:
                            logger.bind(tag=TAG).error(f"MAC地址黑名单数据格式错误: {data}")
                    else:
                        logger.bind(tag=TAG).error(f"获取MAC地址黑名单失败: {response.status}")
            except Exception as e:
                logger.bind(tag=TAG).error(f"请求MAC地址黑名单异常: {str(e)}")

    async def _refresh_device_agent_mapping(self):
        """从API刷新设备-智能体映射关系"""
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            try:
                async with session.get(f"{self.api_base_url}/device/mapping", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "data" in data and isinstance(data["data"], list):
                            self.device_agent_mapping = {
                                item["macAddress"]: item["agentId"]
                                for item in data["data"]
                                if item.get("macAddress") and item.get("agentId")
                            }
                            logger.bind(tag=TAG).info(
                                f"已刷新设备-智能体映射关系，共 {len(self.device_agent_mapping)} 个映射")
                        else:
                            logger.bind(tag=TAG).error(f"设备-智能体映射数据格式错误: {data}")
                    else:
                        logger.bind(tag=TAG).error(f"获取设备-智能体映射失败: {response.status}")
            except Exception as e:
                logger.bind(tag=TAG).error(f"请求设备-智能体映射异常: {str(e)}")

    async def _check_mac_access_rate(self, mac_address: str) -> bool:
        """检查MAC地址访问频率"""
        current_time = time.time()

        # 清理过期记录
        self.mac_access_rate = {
            mac: [t for t in times if current_time - t < self.mac_access_window]
            for mac, times in self.mac_access_rate.items()
        }

        # 获取当前MAC地址的访问记录
        access_times = self.mac_access_rate.get(mac_address, [])

        # 添加当前访问记录
        access_times.append(current_time)
        self.mac_access_rate[mac_address] = access_times

        # 检查是否超过限制
        if len(access_times) > self.mac_access_limit:
            logger.bind(tag=TAG).warning(
                f"MAC地址 {mac_address} 访问频率过高: {len(access_times)}/{self.mac_access_limit}")
            return False

        return True

    def _load_cache_from_file(self):
        """从本地文件加载MAC地址缓存"""
        try:
            # 加载白名单缓存
            if os.path.exists(MAC_CACHE_FILE):
                with open(MAC_CACHE_FILE, 'rb') as f:
                    cache_data = pickle.load(f)
                    # 检查缓存是否过期
                    if 'timestamp' in cache_data and time.time() - cache_data['timestamp'] < self.cache_ttl:
                        self.mac_addresses_cache = cache_data.get('addresses', set())
                        self.last_refresh_time = cache_data.get('timestamp', 0)
                        logger.bind(tag=TAG).info(f"从本地缓存文件加载了 {len(self.mac_addresses_cache)} 个MAC地址")
                    else:
                        logger.bind(tag=TAG).info("本地MAC地址缓存已过期")

            # 加载黑名单缓存
            if os.path.exists(MAC_BLACKLIST_CACHE_FILE):
                with open(MAC_BLACKLIST_CACHE_FILE, 'rb') as f:
                    cache_data = pickle.load(f)
                    # 检查缓存是否过期
                    if 'timestamp' in cache_data and time.time() - cache_data['timestamp'] < self.cache_ttl:
                        self.mac_blacklist = cache_data.get('addresses', set())
                        logger.bind(tag=TAG).info(f"从本地缓存文件加载了 {len(self.mac_blacklist)} 个黑名单MAC地址")
                    else:
                        logger.bind(tag=TAG).info("本地MAC地址黑名单缓存已过期")
        except Exception as e:
            logger.bind(tag=TAG).error(f"加载本地缓存文件失败: {str(e)}")

    def _save_cache_to_file(self):
        """将MAC地址缓存保存到本地文件"""
        retry_count = 0
        max_retries = 3

        while retry_count < max_retries:
            try:
                # 确保缓存目录存在
                os.makedirs(CACHE_DIR, exist_ok=True)

                # 保存白名单缓存
                cache_data = {
                    'addresses': self.mac_addresses_cache,
                    'timestamp': time.time()
                }

                # 使用临时文件避免写入过程中文件损坏
                temp_cache_file = MAC_CACHE_FILE + '.tmp'
                with open(temp_cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)
                    f.flush()  # 确保数据写入磁盘
                    os.fsync(f.fileno())  # 强制同步到磁盘

                # 原子性地移动文件
                os.replace(temp_cache_file, MAC_CACHE_FILE)

                # 保存黑名单缓存
                blacklist_data = {
                    'addresses': self.mac_blacklist,
                    'timestamp': time.time()
                }

                temp_blacklist_file = MAC_BLACKLIST_CACHE_FILE + '.tmp'
                with open(temp_blacklist_file, 'wb') as f:
                    pickle.dump(blacklist_data, f)
                    f.flush()
                    os.fsync(f.fileno())

                os.replace(temp_blacklist_file, MAC_BLACKLIST_CACHE_FILE)

                logger.bind(tag=TAG).info(
                    f"已将 {len(self.mac_addresses_cache)} 个MAC地址和 {len(self.mac_blacklist)} 个黑名单MAC地址保存到本地缓存文件")
                return  # 成功保存，退出重试循环

            except PermissionError as e:
                logger.bind(tag=TAG).error(f"保存缓存文件权限错误 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    time.sleep(0.1)  # 等待100ms后重试
            except OSError as e:
                logger.bind(tag=TAG).error(f"保存缓存文件系统错误 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    time.sleep(0.1)
            except Exception as e:
                logger.bind(tag=TAG).error(f"保存本地缓存文件失败 (尝试 {retry_count + 1}/{max_retries}): {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    time.sleep(0.1)
            finally:
                # 清理可能残留的临时文件
                try:
                    if os.path.exists(MAC_CACHE_FILE + '.tmp'):
                        os.remove(MAC_CACHE_FILE + '.tmp')
                    if os.path.exists(MAC_BLACKLIST_CACHE_FILE + '.tmp'):
                        os.remove(MAC_BLACKLIST_CACHE_FILE + '.tmp')
                except:
                    pass

        if retry_count >= max_retries:
            logger.bind(tag=TAG).error(f"保存缓存文件失败，已达到最大重试次数: {max_retries}")

    async def _check_mac_auth(self, mac_address: str) -> Union[bool, str]:
        """
        检查MAC地址是否有效

        返回值:
        - True: MAC地址有效
        - "blacklisted": MAC地址在黑名单中
        - False: MAC地址无效
        """
        # 1. 首先检查本地内存缓存（最快）
        if mac_address in self.mac_addresses_cache:
            return True

        if mac_address in self.mac_blacklist:
            return "blacklisted"

        # 2. 如果本地缓存未命中，直接查询远程API（不再检查合并配置）
        retry_count = 0
        while retry_count < self.max_retry_attempts:
            try:
                async with aiohttp.ClientSession() as session:
                    headers = {"Authorization": f"Bearer {self.api_key}"}
                    timeout = aiohttp.ClientTimeout(total=10)  # 10秒总超时
                    async with session.get(
                            f"{self.api_base_url}/device/auth/check/{mac_address}",
                            headers=headers,
                            timeout=timeout
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("code") == 0:
                                result = data.get("data", False)
                                if result:
                                    # 更新本地缓存
                                    self.mac_addresses_cache.add(mac_address)
                                    # 异步保存缓存到文件
                                    asyncio.create_task(self._async_save_cache())
                                return result
                            elif data.get("msg") and "禁用" in data.get("msg"):
                                # 更新本地黑名单缓存
                                self.mac_blacklist.add(mac_address)
                                # 异步保存缓存到文件
                                asyncio.create_task(self._async_save_cache())
                                return "blacklisted"
                            else:
                                logger.bind(tag=TAG).error(f"MAC地址验证失败: {data.get('msg')}")
                                return False
                        else:
                            logger.bind(tag=TAG).error(f"MAC地址验证请求失败: {response.status}")
                            # 如果是服务器错误，尝试重试
                            if response.status >= 500:
                                retry_count += 1
                                if retry_count < self.max_retry_attempts:
                                    await asyncio.sleep(1)  # 等待1秒后重试
                                    continue
                            return False
            except asyncio.TimeoutError:
                logger.bind(tag=TAG).error(f"MAC地址验证请求超时")
                retry_count += 1
                if retry_count < self.max_retry_attempts:
                    await asyncio.sleep(1)  # 等待1秒后重试
                    continue
                return False
            except Exception as e:
                logger.bind(tag=TAG).error(f"MAC地址验证异常: {str(e)}")
                retry_count += 1
                if retry_count < self.max_retry_attempts:
                    await asyncio.sleep(1)  # 等待1秒后重试
                    continue
                return False

        # 如果重试次数用完仍未成功，返回False
        return False

    async def _async_save_cache(self):
        """异步保存缓存到文件"""
        try:
            # 在线程池中执行同步的文件保存操作，避免阻塞事件循环
            await asyncio.get_event_loop().run_in_executor(None, self._save_cache_to_file)
        except Exception as e:
            logger.bind(tag=TAG).error(f"异步保存缓存失败: {str(e)}")

    async def _verify_device_fingerprint(self, headers) -> bool:
        """
        验证设备指纹，防止MAC地址伪造

        设备指纹是一组设备特征的组合，可以包括：
        1. 设备型号和固件版本
        2. 网络特征（如IP地址、连接方式）
        3. 行为特征（如请求模式、使用习惯）
        4. 硬件特征（如CPU ID、芯片序列号）

        返回值:
        - True: 设备指纹验证通过
        - False: 设备指纹验证失败
        """
        if not self.enable_device_fingerprint:
            return True

        try:
            # 获取设备MAC地址
            device_mac = headers.get("device-id", "")

            # 获取设备指纹信息
            device_model = headers.get("device-model", "")
            firmware_version = headers.get("firmware-version", "")
            chip_id = headers.get("chip-id", "")

            # 如果缺少必要的指纹信息，验证失败
            if not device_model or not firmware_version or not chip_id:
                logger.bind(tag=TAG).warning(
                    f"设备指纹信息不完整: MAC={device_mac}, Model={device_model}, Firmware={firmware_version}, ChipID={chip_id}")
                return False

            # 查询数据库中存储的设备指纹信息
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {self.api_key}"}
                try:
                    async with session.get(f"{self.api_base_url}/device/fingerprint/{device_mac}",
                                           headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("code") == 0 and data.get("data"):
                                stored_fingerprint = data.get("data")

                                # 比较存储的指纹与当前设备指纹
                                if (stored_fingerprint.get("deviceModel") == device_model and
                                        stored_fingerprint.get("firmwareVersion") == firmware_version and
                                        stored_fingerprint.get("chipId") == chip_id):
                                    logger.bind(tag=TAG).info(f"设备指纹验证通过: {device_mac}")
                                    return True
                                else:
                                    logger.bind(tag=TAG).warning(f"设备指纹不匹配: {device_mac}")
                                    return False
                            elif data.get("code") == 404:  # 设备指纹不存在
                                # 首次连接，记录设备指纹
                                fingerprint_data = {
                                    "macAddress": device_mac,
                                    "deviceModel": device_model,
                                    "firmwareVersion": firmware_version,
                                    "chipId": chip_id
                                }

                                async with session.post(
                                        f"{self.api_base_url}/device/fingerprint",
                                        headers=headers,
                                        json=fingerprint_data
                                ) as post_response:
                                    if post_response.status == 200:
                                        post_data = await post_response.json()
                                        if post_data.get("code") == 0:
                                            logger.bind(tag=TAG).info(f"设备指纹首次记录成功: {device_mac}")
                                            return True
                                        else:
                                            logger.bind(tag=TAG).error(f"设备指纹记录失败: {post_data.get('msg')}")
                                            return False
                                    else:
                                        logger.bind(tag=TAG).error(f"设备指纹记录请求失败: {post_response.status}")
                                        return False
                            else:
                                logger.bind(tag=TAG).error(f"设备指纹验证失败: {data.get('msg')}")
                                return False
                        else:
                            logger.bind(tag=TAG).error(f"设备指纹验证请求失败: {response.status}")
                            return False
                except Exception as e:
                    logger.bind(tag=TAG).error(f"设备指纹验证异常: {str(e)}")
                    return False
        except Exception as e:
            logger.bind(tag=TAG).error(f"设备指纹验证过程异常: {str(e)}")
            return False

    async def _register_mac_address(self, mac_address: str) -> bool:
        """注册MAC地址到白名单"""
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            try:
                # 调用MAC地址自动注册接口
                register_data = {
                    "macAddress": mac_address,
                    "remark": "自动注册设备"  # 设备备注
                }
                async with session.post(
                        f"{self.api_base_url}/device/mac/auto-register",
                        headers=headers,
                        json=register_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            logger.bind(tag=TAG).info(f"设备 {mac_address} 已成功自动注册到白名单")
                            # 添加到本地缓存
                            self.mac_addresses_cache.add(mac_address)
                            # 保存缓存到文件
                            self._save_cache_to_file()
                            return True
                        else:
                            logger.bind(tag=TAG).error(f"设备自动注册失败: {data.get('msg')}")
                            return False
                    else:
                        logger.bind(tag=TAG).error(f"设备自动注册请求失败: {response.status}")
                        return False
            except Exception as e:
                logger.bind(tag=TAG).error(f"设备自动注册异常: {str(e)}")
                return False

    async def get_device_agent_id(self, mac_address: str) -> Optional[str]:
        """获取设备关联的智能体ID"""
        # 如果缓存可能过期，刷新缓存
        current_time = time.time()
        if current_time - self.last_refresh_time > self.cache_ttl:
            await self._refresh_device_agent_mapping()

        return self.device_agent_mapping.get(mac_address)

    async def authenticate(self, headers):
        """验证连接请求，使用MAC地址验证替代token验证"""

        # 保存headers以便其他方法使用
        self.headers = headers

        # 获取设备MAC地址（优先从device-id获取，如果没有则从authorization获取）
        device_mac = headers.get("device-id", "")
        auth_header = headers.get("authorization", "")

        # 如果device-id为空但authorization不为空，尝试从authorization中获取MAC地址
        if not device_mac and auth_header:
            if auth_header.startswith("Bearer "):
                mac_from_auth = auth_header.split(" ")[1]
                if _is_valid_mac_format(mac_from_auth):
                    device_mac = mac_from_auth
                    # 为了兼容性，将MAC地址也设置到device-id中
                    headers["device-id"] = device_mac

        # 检查设备是否在白名单中
        if self.allowed_devices and device_mac in self.allowed_devices:
            logger.bind(tag=TAG).info(f"设备在白名单中: {device_mac}")
            # 添加认证成功标记
            headers["auth_success"] = True
            headers["auth_method"] = "whitelist"
            headers["device_bound"] = True  # 添加设备已绑定标记
            return True

        # 检查MAC地址格式
        if not _is_valid_mac_format(device_mac):
            logger.bind(tag=TAG).error(f"MAC地址格式无效: {device_mac}")
            raise AuthenticationError("认证失败 - MAC地址格式无效，请确保设备MAC地址格式正确")

        logger.bind(tag=TAG).info(f"开始验证MAC地址: {device_mac}, 自动注册开关: {self.auto_register}")

        # 检查MAC地址访问频率
        if not await self._check_mac_access_rate(device_mac):
            raise AuthenticationError("访问频率过高，请稍后再试")

        # 使用单个MAC地址验证接口
        mac_auth_result = await self._check_mac_auth(device_mac)
        logger.bind(tag=TAG).info(f"MAC地址验证结果: {mac_auth_result}")
        if mac_auth_result is True:
            # MAC地址验证通过后，进行设备指纹验证（防止MAC地址伪造）
            if self.enable_device_fingerprint:
                fingerprint_result = await self._verify_device_fingerprint(headers)
                if not fingerprint_result:
                    logger.bind(tag=TAG).warning(f"设备指纹验证失败，可能存在MAC地址伪造: {device_mac}")
                    raise AuthenticationError("设备指纹验证失败，可能存在MAC地址伪造")

            logger.bind(tag=TAG).info(f"MAC地址认证成功: {device_mac}")
            # 添加认证成功标记
            headers["auth_success"] = True
            headers["auth_method"] = "mac"
            headers["device_bound"] = True  # 添加设备已绑定标记
            return True
        elif mac_auth_result == "blacklisted":
            logger.bind(tag=TAG).warning(f"MAC地址 {device_mac} 在黑名单中")
            raise AuthenticationError("MAC地址已被禁用，请联系管理员")
        # 如果MAC地址格式有效但未注册，尝试自动注册
        elif self.auto_register:
            logger.bind(tag=TAG).info(f"MAC地址未注册，开始自动注册: {device_mac}")
            register_result = await self._register_mac_address(device_mac)
            if register_result:
                logger.bind(tag=TAG).info(f"设备自动注册成功: {device_mac}")
                # 添加认证成功标记
                headers["auth_success"] = True
                headers["auth_method"] = "auto_register"
                headers["device_bound"] = True  # 添加设备已绑定标记
                return True
            else:
                # MAC地址格式有效但未注册，抛出特定的未注册MAC地址错误
                logger.bind(tag=TAG).warning(f"MAC地址自动注册失败: {device_mac}")
                raise UnregisteredMacError(device_mac)
        else:
            # MAC地址未注册且不允许自动注册
            logger.bind(tag=TAG).warning(f"未注册的MAC地址: {device_mac}")
            raise UnregisteredMacError(device_mac)

        # 如果执行到这里，说明认证失败
        headers["auth_success"] = False
        headers["auth_reason"] = "MAC地址认证失败"
        logger.bind(tag=TAG).error(f"认证失败 - MAC: {device_mac}")
        raise AuthenticationError("MAC地址认证失败")

    async def _get_device_agent_id(self, mac_address: str) -> Optional[str]:
        """按需获取单个MAC的agentId，优先查本地缓存"""
        try:
            # 检查缓存是否存在且有效（缓存5分钟）
            current_time = time.time()
            if (mac_address in self._device_agent_cache and
                    mac_address in self._device_agent_cache_time and
                    current_time - self._device_agent_cache_time[mac_address] < 300):
                return self._device_agent_cache[mac_address]

            # 缓存不存在或已过期，从API获取
            agent_id = await self._fetch_device_agent_from_api(mac_address)
            if agent_id:
                # 更新缓存
                self._device_agent_cache[mac_address] = agent_id
                self._device_agent_cache_time[mac_address] = current_time
            return agent_id
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取设备agentId失败: {mac_address}, {str(e)}")
            return None

    async def _fetch_device_agent_from_api(self, mac_address: str) -> Optional[str]:
        """异步调用API获取单个MAC的agentId"""
        url = f"{self.api_base_url}/device/mapping/{mac_address}"
        headers = {"Authorization": f"Bearer {self.api_key}"}

        retry_count = 0
        while retry_count < self.max_retry_attempts:
            try:
                async with aiohttp.ClientSession() as session:
                    timeout = aiohttp.ClientTimeout(total=3)
                    async with session.get(url, headers=headers, timeout=timeout) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            if data.get("data") and data["data"].get("agentId"):
                                return data["data"]["agentId"]
                        else:
                            logger.bind(tag=TAG).error(f"API获取单条MAC映射失败: {mac_address}, status: {resp.status}")
                            # 如果是服务器错误，尝试重试
                            if resp.status >= 500:
                                retry_count += 1
                                if retry_count < self.max_retry_attempts:
                                    await asyncio.sleep(1)
                                    continue
                        return None
            except asyncio.TimeoutError:
                logger.bind(tag=TAG).error(f"API获取单条MAC映射超时: {mac_address}")
                retry_count += 1
                if retry_count < self.max_retry_attempts:
                    await asyncio.sleep(1)
                    continue
                return None
            except Exception as e:
                logger.bind(tag=TAG).error(f"API获取单条MAC映射失败: {mac_address}, {str(e)}")
                retry_count += 1
                if retry_count < self.max_retry_attempts:
                    await asyncio.sleep(1)
                    continue
                return None

            # 如果执行到这里，说明请求成功，跳出循环
            break

        return None
